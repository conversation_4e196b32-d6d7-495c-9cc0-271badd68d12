# Cube1 Group 项目架构图和时序图

> 📅 **生成时间**: 2025年8月8日 21:32:46 CST  
> 🎯 **重点关注**: Word 功能模块详细架构分析  
> 📋 **文档类型**: 系统架构图表和时序图  

## 📋 文档概述

本文档包含 Cube1 Group 项目的完整架构图和时序图，特别详细分析了 Word 功能模块的内部架构。所有图表使用 Mermaid 语法生成，便于维护和更新。

## 🏗️ 1. 项目整体架构图

```mermaid
graph TB
    subgraph "🌐 前端应用层 (Next.js 15.1.0)"
        A[app/page.tsx<br/>主应用页面] --> B[app/layout.tsx<br/>全局布局]
        C[components/Matrix.tsx<br/>矩阵主组件] --> D[components/Controls.tsx<br/>控制面板]
        E[components/ui/<br/>基础UI组件库]
    end
    
    subgraph "🎯 Word 功能模块"
        F[WordContainer.tsx<br/>词库管理容器] --> G[WordInputUI.tsx<br/>词语输入界面]
        H[WordStore.ts<br/>词库状态管理] --> I[WordHook.ts<br/>业务逻辑]
        J[WordTypes.ts<br/>类型定义] --> K[WordConfig.ts<br/>配置常量]
    end
    
    subgraph "🔧 Matrix 核心引擎"
        L[MatrixStore.ts<br/>矩阵状态管理] --> M[MatrixHook.ts<br/>核心逻辑]
        N[MatrixTypes.ts<br/>类型系统] --> O[MatrixConfig.ts<br/>配置管理]
    end
    
    subgraph "📊 数据层"
        P[GroupAData.ts<br/>数据集管理] --> Q[stores/<br/>全局状态]
        R[hooks/<br/>共享钩子]
    end
    
    subgraph "🚀 后端服务层 (FastAPI 0.116.1)"
        S[app/main.py<br/>主应用] --> T[api/v1/router.py<br/>API路由]
        U[core/<br/>核心服务] --> V[models/<br/>数据模型]
        W[services/<br/>业务服务] --> X[crud/<br/>数据操作]
    end
    
    subgraph "🗄️ 数据存储层"
        Y[LocalStorage<br/>本地存储] --> Z[PostgreSQL<br/>数据库]
        AA[Redis<br/>缓存层]
    end
    
    subgraph "🛠️ 开发工具链"
        BB[Turbo 2.3.0<br/>Monorepo管理] --> CC[pnpm 9.15.0<br/>包管理]
        DD[TypeScript 5.8.3<br/>类型检查] --> EE[ESLint + Prettier<br/>代码规范]
        FF[Vitest + Playwright<br/>测试框架]
    end
    
    %% 连接关系
    A --> C
    C --> F
    F --> H
    H --> L
    L --> P
    
    C --> S
    S --> Y
    S --> Z
    
    BB --> A
    BB --> S
    
    classDef frontend fill:#e1f5fe
    classDef word fill:#f3e5f5
    classDef matrix fill:#e8f5e8
    classDef data fill:#fff3e0
    classDef backend fill:#fce4ec
    classDef storage fill:#f1f8e9
    classDef tools fill:#f5f5f5
    
    class A,B,C,D,E frontend
    class F,G,H,I,J,K word
    class L,M,N,O matrix
    class P,Q,R data
    class S,T,U,V,W,X backend
    class Y,Z,AA storage
    class BB,CC,DD,EE,FF tools
```

### 架构说明

- **前端应用层**: 基于 Next.js 15.1.0 的现代化前端架构
- **Word 功能模块**: 29个词库管理系统，支持跨词库重复检测
- **Matrix 核心引擎**: 33x33 网格系统，1089个单元格实时渲染
- **数据层**: 基于 Zustand 的响应式状态管理
- **后端服务层**: FastAPI 0.116.1 RESTful API 架构
- **数据存储层**: LocalStorage + PostgreSQL 混合存储方案
- **开发工具链**: Turbo Monorepo + TypeScript 全栈开发环境

## 🎯 2. Word 功能模块详细架构图

```mermaid
graph TB
    subgraph "🎯 Word 功能模块架构"
        subgraph "📱 UI 组件层"
            A[WordContainer.tsx<br/>词库管理主组件]
            B[WordInputUI.tsx<br/>词语输入界面]
            C[WordLibraryItem<br/>词库项组件]
        end
        
        subgraph "🔧 状态管理层"
            D[WordStore.ts<br/>词库数据管理]
            E[WordInputStore.ts<br/>填词模式管理]
            F[WordUIStore.ts<br/>UI状态管理]
        end
        
        subgraph "⚙️ 业务逻辑层"
            G[WordHook.ts<br/>核心业务逻辑]
            H[词语验证逻辑<br/>validateInput]
            I[重复检测逻辑<br/>checkCrossLibraryDuplicate]
            J[词库管理逻辑<br/>addWord/removeWord]
        end
        
        subgraph "📋 类型定义层"
            K[WordTypes.ts<br/>类型定义系统]
            L[WordEntry<br/>词语条目类型]
            M[WordLibrary<br/>词库类型]
            N[WordLibraryState<br/>状态类型]
        end
        
        subgraph "⚙️ 配置层"
            O[WordConfig.ts<br/>配置常量]
            P[WORD_LENGTH_LIMITS<br/>长度限制]
            Q[COLOR_DISPLAY_ORDER<br/>颜色顺序]
            R[AVAILABLE_WORD_LIBRARIES<br/>可用词库]
        end
        
        subgraph "💾 数据持久化"
            S[LocalStorage<br/>本地存储]
            T[Zustand Persist<br/>状态持久化]
            U[Map/Set 序列化<br/>数据结构转换]
        end
    end
    
    %% 组件间关系
    A --> B
    A --> C
    B --> D
    B --> E
    C --> F
    
    D --> G
    E --> G
    F --> G
    
    G --> H
    G --> I
    G --> J
    
    H --> K
    I --> K
    J --> K
    
    K --> L
    K --> M
    K --> N
    
    G --> O
    O --> P
    O --> Q
    O --> R
    
    D --> T
    E --> T
    T --> S
    T --> U
    
    %% 外部依赖
    V[Matrix Store<br/>矩阵状态] -.-> D
    W[Toast Store<br/>消息提示] -.-> G
    
    classDef ui fill:#e3f2fd
    classDef state fill:#f3e5f5
    classDef logic fill:#e8f5e8
    classDef types fill:#fff3e0
    classDef config fill:#fce4ec
    classDef storage fill:#f1f8e9
    classDef external fill:#f5f5f5
    
    class A,B,C ui
    class D,E,F state
    class G,H,I,J logic
    class K,L,M,N types
    class O,P,Q,R config
    class S,T,U storage
    class V,W external
```

### Word 模块特性

- **29个词库系统**: 9种颜色 × 4个级别 - 1个（黑色4级不存在）
- **跨词库重复检测**: 同词库阻止重复，跨词库提醒重复
- **高亮系统**: 重复词语自动分配高亮颜色
- **填词模式**: 键盘导航选择词语绑定到矩阵单元格
- **数据持久化**: 基于 Zustand Persist 的本地存储

## ⏱️ 3. 词语管理操作时序图

```mermaid
sequenceDiagram
    participant User as 👤 用户
    participant UI as 📱 WordInputUI
    participant Store as 🔧 WordStore
    participant Hook as ⚙️ WordHook
    participant Matrix as 🔲 MatrixStore
    participant Toast as 💬 ToastStore
    participant Storage as 💾 LocalStorage
    
    Note over User, Storage: 词语添加流程
    
    User->>UI: 输入词语 "主题"
    UI->>Store: validateInput("black-1", "主题")
    Store->>Hook: 调用验证逻辑
    Hook->>Hook: 检查长度限制 (2-4字符)
    Hook->>Hook: 检查特殊字符
    Hook->>Store: 返回验证结果
    Store->>UI: 返回 {isValid: true, errors: []}
    
    User->>UI: 按下 Enter 确认
    UI->>Store: addWord("black-1", "主题")
    Store->>Hook: createWordEntry("主题", "black", 1)
    Hook->>Hook: 生成唯一ID
    Hook->>Hook: 设置创建时间
    Hook->>Store: 返回 WordEntry
    
    Store->>Store: 检查跨词库重复
    Store->>Store: 更新词库数据
    Store->>Storage: 持久化到 LocalStorage
    Store->>Toast: 显示成功消息
    Store->>UI: 返回操作结果
    UI->>User: 显示词语已添加
    
    Note over User, Storage: 填词模式激活流程
    
    User->>Matrix: 点击矩阵单元格 (10, 15)
    Matrix->>Store: activateWordInput(10, 15, "red", 2)
    Store->>Hook: 查找匹配词库 "red-2"
    Hook->>Store: 获取可用词语列表
    Store->>Store: 设置填词状态
    Store->>UI: 激活填词界面
    UI->>User: 显示词语选择列表
    
    User->>UI: 键盘导航选择词语
    UI->>Store: selectNextWord()
    Store->>Store: 更新选中索引
    Store->>UI: 更新选中状态
    
    User->>UI: 按 Enter 确认选择
    UI->>Store: confirmWordSelection()
    Store->>Matrix: 绑定词语到单元格
    Matrix->>Storage: 持久化绑定关系
    Store->>Store: 更新词语使用统计
    Store->>UI: 退出填词模式
    UI->>User: 显示词语已绑定
    
    Note over User, Storage: 重复检测流程
    
    User->>UI: 输入已存在词语 "主题"
    UI->>Store: validateInput("red-1", "主题")
    Store->>Hook: checkCrossLibraryDuplicate("主题")
    Hook->>Store: 查询全局词语索引
    Store->>Store: 发现重复词库 ["black-1"]
    Store->>Hook: assignWordHighlightColor("主题")
    Hook->>Store: 分配高亮颜色
    Store->>UI: 返回重复警告
    UI->>User: 显示重复提示和高亮
```

### 时序图说明

1. **词语添加流程**: 输入验证 → 创建词条 → 重复检测 → 数据持久化
2. **填词模式流程**: 单元格选择 → 词库匹配 → 键盘导航 → 词语绑定
3. **重复检测流程**: 跨词库查询 → 高亮颜色分配 → 用户提示

## 📊 4. Word 功能模块细节架构图

此图展示了每个模块内部的组件、函数、状态、数据源、配置参数等详细信息：

```mermaid
graph TB
    subgraph Types["📋 WordTypes.ts - 类型定义系统"]
        A1["WordEntry 接口<br/>id, text, color, level<br/>createdAt, usageCount<br/>usagePositions"]
        A2["WordLibrary 接口<br/>key, color, level<br/>words, collapsed<br/>lastUpdated"]
        A3["WordLibraryState 接口<br/>libraries Map<br/>duplicateWords Map<br/>globalWordIndex Map"]
        A4["WordInputState 接口<br/>isActive, selectedCell<br/>availableWords<br/>temporaryWord"]
    end

    subgraph Config["⚙️ WordConfig.ts - 配置常量"]
        B1["词语验证配置<br/>WORD_LENGTH_LIMITS<br/>WORD_VALIDATION_REGEX<br/>字符检测规则"]
        B2["颜色配置<br/>COLOR_DISPLAY_ORDER<br/>AVAILABLE_WORD_LIBRARIES<br/>29个词库组合"]
        B3["高亮颜色配置<br/>HIGHLIGHT_COLORS<br/>assignWordHighlightColor<br/>颜色分配算法"]
    end

    subgraph Store["🔧 WordStore.ts - 状态管理"]
        C1["词库管理方法<br/>addWord, removeWord<br/>updateWord<br/>toggleLibraryCollapse"]
        C2["验证和检测方法<br/>validateInput<br/>checkCrossLibraryDuplicate<br/>updateWordUsage"]
        C3["数据管理方法<br/>exportData<br/>importData<br/>resetAllLibraries"]
        C4["状态数据<br/>libraries Map<br/>duplicateWords Map<br/>globalWordIndex Map"]
    end

    subgraph InputStore["🔧 WordInputStore.ts - 填词模式管理"]
        D1["填词模式方法<br/>activateWordInput<br/>deactivateWordInput<br/>confirmWordSelection"]
        D2["填词状态<br/>isActive boolean<br/>selectedCell<br/>availableWords Array"]
    end

    subgraph Hook["⚙️ WordHook.ts - 业务逻辑"]
        E1["词库工具函数<br/>createEmptyWordLibrary<br/>createWordEntry<br/>buildGlobalWordIndex"]
        E2["验证逻辑<br/>validateWordLength<br/>validateWordCharacters<br/>checkDuplicateInLibrary"]
        E3["初始化函数<br/>createInitialWordLibraryState<br/>创建29个词库<br/>构建全局索引"]
    end

    subgraph Container["📱 WordContainer.tsx - 主容器组件"]
        F1["组件状态<br/>词库列表渲染<br/>激活词库跟踪<br/>滚动行为管理"]
        F2["子组件<br/>WordLibraryItem<br/>词库项渲染<br/>激活状态检测"]
        F3["操作方法<br/>数据导出导入<br/>词库重置<br/>激活词库滚动"]
    end

    subgraph InputUI["📱 WordInputUI.tsx - 输入界面组件"]
        G1["输入状态管理<br/>inputValue string<br/>editingWordId<br/>输入验证状态"]
        G2["事件处理<br/>handleInputChange<br/>handleKeyDown<br/>handleRemoveWord"]
        G3["UI渲染逻辑<br/>词库标题显示<br/>词语列表渲染<br/>错误状态显示"]
        G4["交互功能<br/>实时输入验证<br/>双击编辑词语<br/>使用次数统计"]
    end

    subgraph Storage["💾 数据持久化层"]
        H1["Zustand Persist<br/>状态序列化<br/>Map Set 转换<br/>版本控制"]
        H2["LocalStorage<br/>词库数据存储<br/>绑定关系存储<br/>配置信息存储"]
    end

    %% 依赖关系
    A1 --> C4
    A2 --> C4
    A3 --> C4
    A4 --> D2

    B1 --> E2
    B2 --> E1
    B3 --> C2

    C1 --> E1
    C2 --> E2
    C3 --> H1

    D1 --> E1

    F2 --> G1
    F3 --> C1

    G2 --> C1
    G2 --> C2
    G3 --> A1
    G4 --> D1

    C4 --> H1
    D2 --> H1
    H1 --> H2

    classDef types fill:#e3f2fd
    classDef config fill:#fff3e0
    classDef store fill:#f3e5f5
    classDef input fill:#e8f5e8
    classDef hook fill:#fce4ec
    classDef container fill:#f1f8e9
    classDef ui fill:#e1f5fe
    classDef storage fill:#f5f5f5

    class A1,A2,A3,A4 types
    class B1,B2,B3 config
    class C1,C2,C3,C4 store
    class D1,D2 input
    class E1,E2,E3 hook
    class F1,F2,F3 container
    class G1,G2,G3,G4 ui
    class H1,H2 storage
```

### 细节架构说明

#### 📋 类型定义系统 (WordTypes.ts)
- **WordEntry**: 词语条目的完整数据结构，包含使用统计
- **WordLibrary**: 词库容器，管理同类型词语集合
- **WordLibraryState**: 全局词库状态，包含索引和映射
- **WordInputState**: 填词模式的交互状态

#### ⚙️ 配置常量 (WordConfig.ts)
- **验证规则**: 2-4字符长度限制，中文字符检测
- **颜色系统**: 9种颜色的显示顺序和29个词库组合
- **高亮系统**: 20种预定义颜色的自动分配算法

#### 🔧 状态管理 (WordStore.ts)
- **CRUD操作**: 词语的增删改查和词库管理
- **验证检测**: 实时输入验证和跨词库重复检测
- **数据管理**: 导入导出和批量操作功能

#### ⚙️ 业务逻辑 (WordHook.ts)
- **工具函数**: 词库和词条的创建和管理
- **验证逻辑**: 多层次的输入验证和重复检测
- **初始化**: 系统启动时的数据结构构建

#### 📱 UI组件层
- **WordContainer**: 主容器组件，管理29个词库的显示
- **WordInputUI**: 输入界面组件，处理用户交互和实时验证

#### 💾 数据持久化
- **Zustand Persist**: 状态的序列化和反序列化
- **LocalStorage**: 浏览器本地存储的数据管理

## � 5. Word 功能模块数据流和交互关系图

此图展示了 Word 功能模块中数据的流动路径和各组件间的交互关系：

```mermaid
graph LR
    subgraph UserLayer["🎯 用户交互层"]
        A["用户输入词语"] --> B["实时验证反馈"]
        C["键盘导航"] --> D["词语选择"]
        E["单元格点击"] --> F["填词模式激活"]
    end

    subgraph UILayer["📱 UI组件层数据流"]
        G["WordInputUI.tsx<br/>inputValue 状态<br/>验证错误显示<br/>词语列表渲染"]
        H["WordContainer.tsx<br/>29个词库渲染<br/>激活状态跟踪<br/>滚动行为管理"]
        I["Matrix单元格<br/>词语显示<br/>点击事件<br/>绑定状态"]
    end

    subgraph StateLayer["🔧 状态管理层数据流"]
        J["WordStore<br/>libraries Map<br/>duplicateWords Map<br/>globalWordIndex Map"]
        K["WordInputStore<br/>isActive boolean<br/>selectedCell<br/>availableWords Array"]
        L["MatrixStore<br/>cellWordBindings Map<br/>单元格数据<br/>配置状态"]
    end

    subgraph LogicLayer["⚙️ 业务逻辑层数据流"]
        M["词语验证<br/>长度检查<br/>字符类型检查<br/>特殊字符过滤"]
        N["重复检测<br/>同词库阻止<br/>跨词库提醒<br/>高亮颜色分配"]
        O["词库管理<br/>CRUD操作<br/>索引更新<br/>统计计算"]
    end

    subgraph StorageLayer["💾 数据持久化层"]
        P["Zustand Persist<br/>Map Set 序列化<br/>状态恢复<br/>版本控制"]
        Q["LocalStorage<br/>词库数据<br/>绑定关系<br/>用户配置"]
    end

    subgraph ExternalLayer["🔄 外部系统集成"]
        R["Toast消息系统<br/>成功提示<br/>错误警告<br/>重复提醒"]
        S["Matrix渲染引擎<br/>词语显示<br/>颜色映射<br/>交互响应"]
    end

    %% 数据流连接
    A --> G
    B --> G
    C --> K
    D --> K
    E --> I
    F --> K

    G --> J
    G --> K
    H --> J
    I --> L

    J --> M
    J --> N
    J --> O
    K --> M
    K --> O
    L --> O

    M --> P
    N --> P
    O --> P

    P --> Q

    J --> R
    K --> R
    L --> S

    %% 反馈循环
    R -.-> G
    S -.-> I
    Q -.-> J
    Q -.-> K
    Q -.-> L

    classDef user fill:#e8f5e8
    classDef ui fill:#e3f2fd
    classDef state fill:#f3e5f5
    classDef logic fill:#fff3e0
    classDef storage fill:#f1f8e9
    classDef external fill:#fce4ec

    class A,B,C,D,E,F user
    class G,H,I ui
    class J,K,L state
    class M,N,O logic
    class P,Q storage
    class R,S external
```

### 数据流说明

#### 🎯 用户交互层

- **输入验证**: 用户输入 → 实时验证 → 即时反馈
- **键盘导航**: 方向键 → 词语选择 → 状态更新
- **单元格交互**: 点击 → 填词模式 → 词语绑定

#### 📱 UI组件层数据流

- **WordInputUI**: 管理输入状态、验证显示、词语渲染
- **WordContainer**: 控制29个词库的显示和激活状态
- **Matrix单元格**: 处理词语显示和用户交互事件

#### 🔧 状态管理层数据流

- **WordStore**: 核心词库数据和索引管理
- **WordInputStore**: 填词模式的交互状态
- **MatrixStore**: 单元格绑定关系和矩阵数据

#### ⚙️ 业务逻辑层数据流

- **词语验证**: 多层次验证规则和错误处理
- **重复检测**: 跨词库查询和高亮颜色分配
- **词库管理**: CRUD操作和数据一致性维护

#### 💾 数据持久化层

- **Zustand Persist**: 状态序列化和版本控制
- **LocalStorage**: 浏览器本地数据存储

#### 🔄 外部系统集成

- **Toast系统**: 用户操作反馈和消息提示
- **Matrix引擎**: 词语在网格中的可视化渲染

### 关键数据流路径

1. **词语添加流程**: 用户输入 → UI验证 → Store处理 → 业务逻辑 → 持久化
2. **填词模式流程**: 单元格点击 → 模式激活 → 词语选择 → 绑定确认 → 状态更新
3. **重复检测流程**: 输入检测 → 跨库查询 → 高亮分配 → 用户提示
4. **数据同步流程**: 状态变更 → 持久化 → 外部系统通知 → UI更新

## �🔗 相关文档

- [项目整体架构文档](../README.md)
- [Word 功能模块设计文档](../reports/word-module-design.md)
- [Matrix 核心引擎文档](../reports/matrix-core-architecture.md)
- [状态管理架构文档](../reports/state-management-architecture.md)

## 📝 维护说明

1. **图表更新**: 当架构发生变化时，请及时更新对应的 Mermaid 图表
2. **版本控制**: 重大架构变更请创建新版本的架构图
3. **文档同步**: 确保架构图与实际代码实现保持一致
4. **定期审查**: 建议每月审查一次架构图的准确性

---

> 📋 **文档维护**: 请在架构变更时及时更新此文档  
> 🔄 **最后更新**: 2025年8月8日 21:32:46 CST  
> 👥 **维护团队**: Cube1 Group 开发团队

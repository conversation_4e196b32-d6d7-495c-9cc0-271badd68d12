{"$schema": "https://turbo.build/schema.json", "ui": "tui", "globalDependencies": ["**/.env.*local"], "tasks": {"build": {"dependsOn": ["^build", "type-check"], "outputs": [".next/**", "!.next/cache/**", "dist/**", "build/**"], "env": ["NODE_ENV", "NEXT_PUBLIC_*"]}, "dev": {"cache": false, "persistent": true, "env": ["NODE_ENV", "NEXT_PUBLIC_*"]}, "lint": {"dependsOn": ["^lint"], "outputs": []}, "lint:fix": {"dependsOn": ["^lint:fix"], "outputs": []}, "test": {"dependsOn": ["^build"], "outputs": ["coverage/**", "test-results/**"], "env": ["NODE_ENV"]}, "test:e2e": {"dependsOn": ["build"], "outputs": ["test-results/**", "playwright-report/**"], "env": ["NODE_ENV"]}, "type-check": {"dependsOn": ["^type-check"], "outputs": []}, "format": {"outputs": []}, "format:check": {"outputs": []}, "clean": {"cache": false}}}
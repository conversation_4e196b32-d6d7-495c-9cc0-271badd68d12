/**
 * 矩阵系统 E2E 测试
 * 🎯 核心价值：验证33x33网格系统的完整功能和性能
 * 📦 测试范围：网格渲染、交互、性能、数据持久化
 * 🔄 测试策略：端到端功能测试 + 性能基准测试
 * 
 * 更新时间：2025年8月8日
 */

import { test, expect, type Page } from '@playwright/test';

// ===== 测试配置 =====

const MATRIX_SIZE = 33;
const TOTAL_CELLS = MATRIX_SIZE * MATRIX_SIZE; // 1089
const BASE_URL = 'http://localhost:4096';

// ===== 辅助函数 =====

/**
 * 等待矩阵完全加载
 */
async function waitForMatrixLoad(page: Page) {
  // 等待矩阵容器出现
  await page.waitForSelector('[data-testid="matrix-container"]', { timeout: 10000 });
  
  // 等待所有单元格渲染完成
  await page.waitForFunction(
    (expectedCount) => {
      const cells = document.querySelectorAll('[data-testid^="cell-"]');
      return cells.length === expectedCount;
    },
    TOTAL_CELLS,
    { timeout: 15000 }
  );
}

/**
 * 获取指定坐标的单元格
 */
async function getCell(page: Page, x: number, y: number) {
  return page.locator(`[data-testid="cell-${x}-${y}"]`);
}

/**
 * 测量渲染性能
 */
async function measureRenderPerformance(page: Page) {
  const startTime = Date.now();
  await waitForMatrixLoad(page);
  const endTime = Date.now();
  return endTime - startTime;
}

// ===== 基础功能测试 =====

test.describe('矩阵系统基础功能', () => {
  test.beforeEach(async ({ page }) => {
    await page.goto(BASE_URL);
  });

  test('应该正确渲染33x33网格', async ({ page }) => {
    await waitForMatrixLoad(page);
    
    // 验证矩阵容器存在
    const matrixContainer = page.locator('[data-testid="matrix-container"]');
    await expect(matrixContainer).toBeVisible();
    
    // 验证单元格数量
    const cells = page.locator('[data-testid^="cell-"]');
    await expect(cells).toHaveCount(TOTAL_CELLS);
    
    // 验证第一个和最后一个单元格
    const firstCell = await getCell(page, 0, 0);
    const lastCell = await getCell(page, 32, 32);
    
    await expect(firstCell).toBeVisible();
    await expect(lastCell).toBeVisible();
  });

  test('应该正确显示坐标信息', async ({ page }) => {
    await waitForMatrixLoad(page);
    
    // 检查几个关键坐标点
    const testCoordinates = [
      { x: 0, y: 0 },
      { x: 16, y: 16 }, // 中心点
      { x: 32, y: 32 },
    ];
    
    for (const coord of testCoordinates) {
      const cell = await getCell(page, coord.x, coord.y);
      await expect(cell).toContainText(`${coord.x},${coord.y}`);
    }
  });

  test('应该支持单元格点击选择', async ({ page }) => {
    await waitForMatrixLoad(page);
    
    const testCell = await getCell(page, 5, 5);
    
    // 点击单元格
    await testCell.click();
    
    // 验证选中状态
    await expect(testCell).toHaveClass(/selected/);
    
    // 再次点击应该取消选择
    await testCell.click();
    await expect(testCell).not.toHaveClass(/selected/);
  });

  test('应该支持单元格悬停效果', async ({ page }) => {
    await waitForMatrixLoad(page);
    
    const testCell = await getCell(page, 10, 10);
    
    // 悬停
    await testCell.hover();
    await expect(testCell).toHaveClass(/hovered/);
    
    // 移开鼠标
    await page.mouse.move(0, 0);
    await expect(testCell).not.toHaveClass(/hovered/);
  });
});

// ===== 性能测试 =====

test.describe('矩阵系统性能测试', () => {
  test('矩阵渲染性能应该在合理范围内', async ({ page }) => {
    const renderTime = await measureRenderPerformance(page);
    
    // 1089个单元格应该在3秒内完成渲染
    expect(renderTime).toBeLessThan(3000);
    
    console.log(`矩阵渲染时间: ${renderTime}ms`);
  });

  test('大量单元格交互应该保持流畅', async ({ page }) => {
    await page.goto(BASE_URL);
    await waitForMatrixLoad(page);
    
    const startTime = Date.now();
    
    // 快速点击多个单元格
    for (let i = 0; i < 10; i++) {
      const cell = await getCell(page, i, i);
      await cell.click();
    }
    
    const endTime = Date.now();
    const interactionTime = endTime - startTime;
    
    // 10次点击应该在1秒内完成
    expect(interactionTime).toBeLessThan(1000);
    
    console.log(`10次单元格交互时间: ${interactionTime}ms`);
  });

  test('滚动性能应该流畅', async ({ page }) => {
    await page.goto(BASE_URL);
    await waitForMatrixLoad(page);
    
    const matrixContainer = page.locator('[data-testid="matrix-container"]');
    
    // 测量滚动性能
    const startTime = Date.now();
    
    // 执行滚动操作
    await matrixContainer.hover();
    await page.mouse.wheel(0, 500);
    await page.waitForTimeout(100);
    await page.mouse.wheel(0, -500);
    
    const endTime = Date.now();
    const scrollTime = endTime - startTime;
    
    // 滚动操作应该在500ms内完成
    expect(scrollTime).toBeLessThan(500);
    
    console.log(`滚动操作时间: ${scrollTime}ms`);
  });
});

// ===== 业务模式测试 =====

test.describe('业务模式切换', () => {
  test.beforeEach(async ({ page }) => {
    await page.goto(BASE_URL);
    await waitForMatrixLoad(page);
  });

  test('应该支持坐标模式', async ({ page }) => {
    // 切换到坐标模式
    await page.click('[data-testid="mode-coordinate"]');
    
    // 验证坐标显示
    const cell = await getCell(page, 0, 0);
    await expect(cell).toContainText('0,0');
  });

  test('应该支持颜色模式', async ({ page }) => {
    // 切换到颜色模式
    await page.click('[data-testid="mode-color"]');
    
    // 验证颜色显示
    const cells = page.locator('[data-testid^="cell-"]');
    const firstCell = cells.first();
    
    // 应该有颜色相关的类名
    await expect(firstCell).toHaveClass(/bg-/);
  });
});

// ===== 数据持久化测试 =====

test.describe('数据持久化', () => {
  test('应该保存和恢复矩阵状态', async ({ page }) => {
    await page.goto(BASE_URL);
    await waitForMatrixLoad(page);
    
    // 选择一些单元格
    await (await getCell(page, 1, 1)).click();
    await (await getCell(page, 2, 2)).click();
    
    // 刷新页面
    await page.reload();
    await waitForMatrixLoad(page);
    
    // 验证状态是否保持
    await expect(await getCell(page, 1, 1)).toHaveClass(/selected/);
    await expect(await getCell(page, 2, 2)).toHaveClass(/selected/);
  });
});

// ===== 响应式测试 =====

test.describe('响应式设计', () => {
  test('应该在不同屏幕尺寸下正常工作', async ({ page }) => {
    // 测试桌面尺寸
    await page.setViewportSize({ width: 1920, height: 1080 });
    await page.goto(BASE_URL);
    await waitForMatrixLoad(page);
    
    let matrixContainer = page.locator('[data-testid="matrix-container"]');
    await expect(matrixContainer).toBeVisible();
    
    // 测试平板尺寸
    await page.setViewportSize({ width: 768, height: 1024 });
    await expect(matrixContainer).toBeVisible();
    
    // 测试手机尺寸
    await page.setViewportSize({ width: 375, height: 667 });
    await expect(matrixContainer).toBeVisible();
  });
});

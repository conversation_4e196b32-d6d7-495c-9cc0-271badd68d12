/**
 * Vitest 测试环境设置
 * 🎯 核心价值：统一的测试环境配置，确保测试的一致性和可靠性
 * 📦 功能范围：全局测试配置、模拟设置、测试工具初始化
 * 🔄 架构设计：基于Vitest的现代化测试环境
 * 
 * 更新时间：2025年8月8日
 */

import '@testing-library/jest-dom';
import { vi } from 'vitest';

// ===== 全局模拟设置 =====

// 模拟 localStorage
const localStorageMock = {
  getItem: vi.fn(),
  setItem: vi.fn(),
  removeItem: vi.fn(),
  clear: vi.fn(),
  length: 0,
  key: vi.fn(),
};

Object.defineProperty(window, 'localStorage', {
  value: localStorageMock,
  writable: true,
});

// 模拟 sessionStorage
const sessionStorageMock = {
  getItem: vi.fn(),
  setItem: vi.fn(),
  removeItem: vi.fn(),
  clear: vi.fn(),
  length: 0,
  key: vi.fn(),
};

Object.defineProperty(window, 'sessionStorage', {
  value: sessionStorageMock,
  writable: true,
});

// 模拟 ResizeObserver
global.ResizeObserver = vi.fn().mockImplementation(() => ({
  observe: vi.fn(),
  unobserve: vi.fn(),
  disconnect: vi.fn(),
}));

// 模拟 IntersectionObserver
global.IntersectionObserver = vi.fn().mockImplementation(() => ({
  observe: vi.fn(),
  unobserve: vi.fn(),
  disconnect: vi.fn(),
}));

// 模拟 matchMedia
Object.defineProperty(window, 'matchMedia', {
  writable: true,
  value: vi.fn().mockImplementation(query => ({
    matches: false,
    media: query,
    onchange: null,
    addListener: vi.fn(), // deprecated
    removeListener: vi.fn(), // deprecated
    addEventListener: vi.fn(),
    removeEventListener: vi.fn(),
    dispatchEvent: vi.fn(),
  })),
});

// ===== 测试环境配置 =====

// 设置测试超时时间
vi.setConfig({
  testTimeout: 10000,
});

// 全局测试前置操作
beforeEach(() => {
  // 清理所有模拟
  vi.clearAllMocks();
  
  // 重置 localStorage
  localStorageMock.getItem.mockClear();
  localStorageMock.setItem.mockClear();
  localStorageMock.removeItem.mockClear();
  localStorageMock.clear.mockClear();
  
  // 重置 sessionStorage
  sessionStorageMock.getItem.mockClear();
  sessionStorageMock.setItem.mockClear();
  sessionStorageMock.removeItem.mockClear();
  sessionStorageMock.clear.mockClear();
});

// 全局测试后置操作
afterEach(() => {
  // 清理DOM
  document.body.innerHTML = '';
  
  // 清理定时器
  vi.clearAllTimers();
});

// ===== 测试工具函数 =====

/**
 * 创建测试用的矩阵数据
 */
export const createTestMatrixData = (size: number = 33) => {
  const data = new Map();
  for (let x = 0; x < size; x++) {
    for (let y = 0; y < size; y++) {
      data.set(`${x},${y}`, {
        x,
        y,
        color: 'red' as const,
        level: 1 as const,
        content: '',
        isSelected: false,
        isHovered: false,
      });
    }
  }
  return data;
};

/**
 * 创建测试用的词库数据
 */
export const createTestWordLibrary = () => {
  return new Map([
    ['red-1', new Map([['word1', { id: 'word1', text: '测试词语1', color: 'red', level: 1 }]])],
    ['blue-2', new Map([['word2', { id: 'word2', text: '测试词语2', color: 'blue', level: 2 }]])],
  ]);
};

/**
 * 等待异步操作完成
 */
export const waitFor = (ms: number = 0) => {
  return new Promise(resolve => setTimeout(resolve, ms));
};

/**
 * 模拟用户点击事件
 */
export const mockUserClick = (element: HTMLElement) => {
  const event = new MouseEvent('click', {
    bubbles: true,
    cancelable: true,
  });
  element.dispatchEvent(event);
};

/**
 * 模拟键盘事件
 */
export const mockKeyboardEvent = (key: string, element: HTMLElement = document.body) => {
  const event = new KeyboardEvent('keydown', {
    key,
    bubbles: true,
    cancelable: true,
  });
  element.dispatchEvent(event);
};

// ===== 性能测试工具 =====

/**
 * 测试渲染性能
 */
export const measureRenderTime = async (renderFn: () => void) => {
  const start = performance.now();
  renderFn();
  await waitFor(0); // 等待下一个事件循环
  const end = performance.now();
  return end - start;
};

/**
 * 测试大量数据渲染性能
 */
export const testLargeDatasetPerformance = async (dataSize: number, renderFn: (data: any) => void) => {
  const testData = Array.from({ length: dataSize }, (_, i) => ({
    id: i,
    value: `item-${i}`,
  }));
  
  const renderTime = await measureRenderTime(() => renderFn(testData));
  
  return {
    dataSize,
    renderTime,
    itemsPerMs: dataSize / renderTime,
  };
};

// ===== 导出测试配置 =====

export const testConfig = {
  matrixSize: 33,
  maxRenderTime: 100, // 最大渲染时间（毫秒）
  performanceThreshold: 10, // 性能阈值（项目/毫秒）
};

console.log('✅ 测试环境设置完成 - Vitest + Testing Library');

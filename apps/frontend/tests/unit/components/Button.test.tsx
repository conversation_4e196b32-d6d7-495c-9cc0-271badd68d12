/**
 * Button 组件单元测试
 * 🎯 核心价值：确保按钮组件的功能正确性和样式一致性
 * 📦 测试范围：按钮变体、尺寸、状态、交互行为
 * 🔄 测试策略：快照测试 + 行为测试 + 可访问性测试
 * 
 * 更新时间：2025年8月8日
 */

import { render, screen, fireEvent } from '@testing-library/react';
import { describe, it, expect, vi } from 'vitest';
import { Button } from '@/components/ui/Button';

describe('Button Component', () => {
  // ===== 基础渲染测试 =====
  
  it('应该正确渲染基础按钮', () => {
    render(<Button>测试按钮</Button>);
    
    const button = screen.getByRole('button', { name: '测试按钮' });
    expect(button).toBeInTheDocument();
    expect(button).toHaveTextContent('测试按钮');
  });

  it('应该应用默认样式', () => {
    render(<Button>默认按钮</Button>);
    
    const button = screen.getByRole('button');
    expect(button).toHaveClass('inline-flex', 'items-center', 'justify-center');
  });

  // ===== 变体测试 =====
  
  it('应该正确应用primary变体样式', () => {
    render(<Button variant="primary">主要按钮</Button>);
    
    const button = screen.getByRole('button');
    expect(button).toHaveClass('bg-blue-600', 'text-white');
  });

  it('应该正确应用secondary变体样式', () => {
    render(<Button variant="secondary">次要按钮</Button>);
    
    const button = screen.getByRole('button');
    expect(button).toHaveClass('bg-gray-100', 'text-gray-900');
  });

  it('应该正确应用danger变体样式', () => {
    render(<Button variant="danger">危险按钮</Button>);
    
    const button = screen.getByRole('button');
    expect(button).toHaveClass('bg-red-600', 'text-white');
  });

  it('应该正确应用icon变体样式', () => {
    render(<Button variant="icon">图标</Button>);
    
    const button = screen.getByRole('button');
    expect(button).toHaveClass('bg-transparent');
  });

  // ===== 尺寸测试 =====
  
  it('应该正确应用小尺寸样式', () => {
    render(<Button size="sm">小按钮</Button>);
    
    const button = screen.getByRole('button');
    expect(button).toHaveClass('px-2', 'py-1', 'text-sm');
  });

  it('应该正确应用中等尺寸样式', () => {
    render(<Button size="md">中等按钮</Button>);
    
    const button = screen.getByRole('button');
    expect(button).toHaveClass('px-4', 'py-2', 'text-base');
  });

  it('应该正确应用大尺寸样式', () => {
    render(<Button size="lg">大按钮</Button>);
    
    const button = screen.getByRole('button');
    expect(button).toHaveClass('px-6', 'py-3', 'text-lg');
  });

  // ===== 状态测试 =====
  
  it('应该正确处理激活状态', () => {
    render(<Button active>激活按钮</Button>);
    
    const button = screen.getByRole('button');
    expect(button).toHaveClass('bg-gray-200');
  });

  it('应该正确处理禁用状态', () => {
    render(<Button disabled>禁用按钮</Button>);
    
    const button = screen.getByRole('button');
    expect(button).toBeDisabled();
    expect(button).toHaveClass('opacity-50', 'cursor-not-allowed');
  });

  // ===== 交互测试 =====
  
  it('应该正确处理点击事件', () => {
    const handleClick = vi.fn();
    render(<Button onClick={handleClick}>可点击按钮</Button>);
    
    const button = screen.getByRole('button');
    fireEvent.click(button);
    
    expect(handleClick).toHaveBeenCalledTimes(1);
  });

  it('禁用状态下不应该触发点击事件', () => {
    const handleClick = vi.fn();
    render(<Button onClick={handleClick} disabled>禁用按钮</Button>);
    
    const button = screen.getByRole('button');
    fireEvent.click(button);
    
    expect(handleClick).not.toHaveBeenCalled();
  });

  // ===== 可访问性测试 =====
  
  it('应该支持键盘导航', () => {
    const handleClick = vi.fn();
    render(<Button onClick={handleClick}>键盘按钮</Button>);
    
    const button = screen.getByRole('button');
    button.focus();
    
    expect(button).toHaveFocus();
    
    fireEvent.keyDown(button, { key: 'Enter' });
    expect(handleClick).toHaveBeenCalledTimes(1);
    
    fireEvent.keyDown(button, { key: ' ' });
    expect(handleClick).toHaveBeenCalledTimes(2);
  });

  it('应该有正确的ARIA属性', () => {
    render(<Button aria-label="自定义标签">按钮</Button>);
    
    const button = screen.getByRole('button');
    expect(button).toHaveAttribute('aria-label', '自定义标签');
  });

  // ===== 自定义样式测试 =====
  
  it('应该正确应用自定义className', () => {
    render(<Button className="custom-class">自定义按钮</Button>);
    
    const button = screen.getByRole('button');
    expect(button).toHaveClass('custom-class');
  });

  // ===== 性能测试 =====
  
  it('应该在合理时间内渲染', async () => {
    const start = performance.now();
    
    render(<Button>性能测试按钮</Button>);
    
    const end = performance.now();
    const renderTime = end - start;
    
    // 渲染时间应该小于10毫秒
    expect(renderTime).toBeLessThan(10);
  });
});

import path from 'path';
/** @type {import('next').NextConfig} */
const nextConfig = {
    // ESLint配置 - 恢复构建时的代码检查
    eslint: {
        ignoreDuringBuilds: false, // ✅ 恢复ESLint检查
    },
    // 生产环境输出配置
    output: 'standalone',
    // 自定义源码目录
    pageExtensions: ['tsx', 'ts', 'jsx', 'js'],
    // TypeScript配置 - 严格类型检查
    typescript: {
        ignoreBuildErrors: false, // ✅ 严格类型检查
    },
    // 添加实验性功能配置
    experimental: {
        // 启用turbopack以提升开发体验
        turbo: {
            rules: {
                '*.svg': {
                    loaders: ['@svgr/webpack'],
                    as: '*.js',
                },
            },
        },
    },
    // Next.js 14+ 默认支持 App Router
    // 重写路径以支持frontend目录结构
    webpack: (config) => {
        // 添加路径别名（与 tsconfig.json 保持一致）
        config.resolve.alias = {
            ...config.resolve.alias,
            '@': path.resolve('.'),
            '@/components': path.resolve('./components'),
            '@/lib': path.resolve('./lib'),
            '@/utils': path.resolve('./lib/utils'),
            '@/types': path.resolve('./lib/types'),
            '@/stores': path.resolve('./stores'),
            '@/features': path.resolve('./features'),
            '@/api': path.resolve('./app/api'),
            '@/app': path.resolve('./app'),
        };
        return config;
    },
};
export default nextConfig;

/**
 * 词库输入组件
 * 🎯 核心价值：一体化词库输入框，支持展开/折叠和横向滚动
 * 📦 功能范围：词库标题显示、词语列表展示、输入框、展开折叠控制
 * 🔄 架构设计：一体化容器设计，与矩阵系统颜色同步
 */

'use client';

import { toast } from '@/components/ui/Toast';
import type { BasicColorType, DataLevel } from '@/features/matrix/MatrixTypes';
import {
  getWordLibraryBackgroundColor,
  getWordLibraryShortName,
  getWordLibraryTextColor
} from '@/features/word/WordConfig';
import { parseInputText } from '@/features/word/WordHook';
import { useWordInputStore, useWordStore } from '@/features/word/WordStore';
import type { WordLibraryKey } from '@/features/word/WordTypes';
import React, { useCallback, useEffect, useRef, useState } from 'react';

// ===== 组件属性 =====

interface WordInputProps {
  /** 词库标识 */
  libraryKey: WordLibraryKey;
  /** 颜色 */
  color: BasicColorType;
  /** 级别 */
  level: DataLevel;
  /** 是否折叠状态 */
  collapsed?: boolean;
  /** 占位符文本 */
  placeholder?: string;
  /** 自定义类名 */
  className?: string;
  /** 输入变化回调 */
  onChange?: (words: string[]) => void;
  /** 是否为当前激活的词库 */
  isActiveLibrary?: boolean;
}

// ===== 主组件 =====

const WordInput: React.FC<WordInputProps> = ({
  libraryKey,
  color,
  level,
  collapsed: _collapsed = false,
  placeholder = '请输入...',
  className = '',
  onChange,
  isActiveLibrary = false
}) => {
  const inputRef = useRef<HTMLInputElement>(null);
  const [inputValue, setInputValue] = useState('');
  const [editingWordId, setEditingWordId] = useState<string | null>(null);
  const [editingText, setEditingText] = useState('');

  // 获取词库状态
  const {
    getLibrary,
    validateInput,
    addWord,
    removeWord,
    updateWord,
    toggleLibraryCollapse,
    checkCrossLibraryDuplicate,
    getWordHighlightColor
  } = useWordStore();

  // 获取填词模式状态
  const { temporaryWord, selectedWordIndex } = useWordInputStore();

  // 滚动容器的引用
  const scrollContainerRef = useRef<HTMLDivElement>(null);
  const library = getLibrary(libraryKey);

  // 简化的滚动到指定元素
  const scrollToElement = useCallback((element: HTMLElement) => {
    element.scrollIntoView({
      behavior: 'smooth',
      block: 'nearest',
      inline: 'center'
    });
  }, []);

  const isCollapsed = Boolean(library?.collapsed);

  // 当当前词库被激活时，强制切换为折叠状态以启用横向滚动
  useEffect(() => {
    if (!library) return;
    if (isActiveLibrary && !isCollapsed) {
      toggleLibraryCollapse(libraryKey);
    }
  }, [isActiveLibrary, isCollapsed, libraryKey, toggleLibraryCollapse, library]);

  // 当选中词语变化时，滚动到可视区域
  useEffect(() => {
    if (!isActiveLibrary || !library || !isCollapsed) return;
    if (selectedWordIndex === undefined || selectedWordIndex === null) return;

    const container = scrollContainerRef.current;
    if (!container) return;

    const words = library.words || [];
    if (selectedWordIndex >= 0 && selectedWordIndex < words.length) {
      const wordId = words[selectedWordIndex].id;
      const selectedElement = container.querySelector(`[data-word-id="${wordId}"]`) as HTMLElement;
      if (selectedElement) {
        scrollToElement(selectedElement);
      }
    }
  }, [isActiveLibrary, isCollapsed, selectedWordIndex, libraryKey, library, scrollToElement]);

  const shortName = getWordLibraryShortName(color, level);
  const backgroundColor = getWordLibraryBackgroundColor(color);
  const textColor = getWordLibraryTextColor(color);

  // 显示错误提示
  const showErrorMessage = useCallback((message: string) => {
    toast.controlPanel.error(message, 3000);
  }, []);

  // 处理输入确认（逗号或回车）
  const handleInputConfirm = useCallback(() => {
    if (!inputValue.trim()) return;

    // 解析输入文本，过滤空词语
    const newWords = parseInputText(inputValue).filter(word => word.trim().length > 0);
    if (newWords.length === 0) {
      setInputValue('');
      return;
    }

    const validWords: string[] = [];
    const errorWords: string[] = [];
    const duplicateWords: string[] = [];

    // 逐个验证和添加词语
    newWords.forEach(word => {
      const trimmedWord = word.trim();

      // 先进行输入验证（阻止性验证）
      const validation = validateInput(libraryKey, trimmedWord);
      if (validation.isValid) {
        // 验证通过，尝试添加词语
        const result = addWord(libraryKey, trimmedWord);
        if (result.isValid) {
          validWords.push(trimmedWord);

          // 检查是否为跨词库重复词语，显示提醒（提醒性检测）
          if (result.isDuplicate && result.duplicateLibraries && result.duplicateLibraries.length > 1) {
            const otherLibraries = result.duplicateLibraries.filter(lib => lib !== libraryKey);
            if (otherLibraries.length > 0) {
              duplicateWords.push(trimmedWord);
            }
          }
        } else {
          // 添加失败（通常是同词库内重复）
          errorWords.push(trimmedWord);
        }
      } else {
        // 验证失败
        errorWords.push(trimmedWord);
        console.warn(`词语"${trimmedWord}"验证失败:`, validation.errors);
      }
    });

    // 清空输入框
    setInputValue('');

    // 显示提示信息
    if (errorWords.length > 0) {
      showErrorMessage(`无效或重复词语: ${errorWords.join(', ')}`);
    }

    if (duplicateWords.length > 0) {
      toast.controlPanel.warning(`"${duplicateWords.join('", "')}"在其他词库中存在`, 3000);
    }

    // 触发回调
    if (validWords.length > 0) {
      onChange?.(validWords);
    }
  }, [inputValue, libraryKey, validateInput, addWord, onChange, showErrorMessage]);

  // 处理输入变化
  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    let value = e.target.value;

    // 保持中文逗号格式，统一处理
    value = value.replace(/,/g, '，');

    setInputValue(value);

    // 实时检测逗号并自动确认输入
    if (value.endsWith('，') && value.trim().length > 1) {
      const beforeComma = value.slice(0, -1).trim();
      if (beforeComma.length > 0) {
        setTimeout(() => {
          if (inputRef.current && inputRef.current.value.endsWith('，')) {
            handleInputConfirm();
          }
        }, 300);
      }
    }
  };

  // 处理键盘事件
  const handleKeyDown = (e: React.KeyboardEvent<HTMLInputElement>) => {
    if (e.key === 'Enter') {
      e.preventDefault();

      if (inputValue.trim()) {
        let formattedValue = inputValue.trim();

        if (!formattedValue.endsWith(',') && !formattedValue.endsWith('，')) {
          formattedValue += '，';
        }

        formattedValue = formattedValue.replace(/,\s*/g, '，');
        setInputValue(formattedValue);

        setTimeout(() => {
          handleInputConfirm();
        }, 200);
      } else {
        handleInputConfirm();
      }
    }
  };

  // 删除词语
  const handleRemoveWord = (wordId: string) => {
    if (library) {
      removeWord(libraryKey, wordId);
    }
  };

  // 切换展开/折叠
  const handleToggleExpanded = () => {
    toggleLibraryCollapse(libraryKey);
  };

  // 开始编辑词语
  const handleStartEdit = (wordId: string, currentText: string) => {
    if (library && !library.collapsed) {
      setEditingWordId(wordId);
      setEditingText(currentText);
    }
  };

  // 取消编辑
  const handleCancelEdit = () => {
    setEditingWordId(null);
    setEditingText('');
  };

  // 保存编辑
  const handleSaveEdit = () => {
    if (editingWordId && editingText.trim()) {
      const result = updateWord(libraryKey, editingWordId, editingText.trim());

      if (result.isValid) {
        toast.controlPanel.success('词语更新成功', 2000);
        if (result.isDuplicate && result.duplicateLibraries.length > 1) {
          toast.controlPanel.warning(`词语"${editingText.trim()}"在其他词库中存在`, 3000);
        }
      } else {
        showErrorMessage(`未更改`);
      }
    }

    handleCancelEdit();
  };

  // 处理编辑输入键盘事件
  const handleEditKeyDown = (e: React.KeyboardEvent<HTMLInputElement>) => {
    if (e.key === 'Enter') {
      e.preventDefault();
      handleSaveEdit();
    } else if (e.key === 'Escape') {
      e.preventDefault();
      handleCancelEdit();
    }
  };

  if (!library) {
    return null;
  }

  return (
    <div
      className={`word-input-container relative rounded-lg border transition-all duration-200 ${className}`}
      style={{
        backgroundColor: `${backgroundColor}20`,
        borderColor: `${backgroundColor}40`,
        color: textColor
      }}
    >
      <div className="p-3">
        <div className="flex items-center gap-3">
          <div
            ref={scrollContainerRef}
            className="flex-1"
            style={{
              overflowX: isCollapsed ? 'auto' : 'visible',
              overflowY: isCollapsed ? 'hidden' : 'visible',
              minHeight: isCollapsed ? '32px' : 'auto',
            }}
          >
            <div
              className={`flex items-center gap-0 text-sm ${isCollapsed ? 'flex-nowrap' : 'flex-wrap'}`}
              style={{
                width: isCollapsed ? 'max-content' : '100%',
                flexShrink: isCollapsed ? 0 : 1,
              }}
            >
              {/* 词库标题部分 */}
              <span
                className="font-bold mr-1 flex-shrink-0 cursor-pointer hover:bg-black hover:bg-opacity-10 p-1 rounded-md transition-colors"
                style={{
                  color: textColor,
                  textDecoration: isCollapsed ? 'underline' : 'none'
                }}
                onClick={handleToggleExpanded}
                title={!isCollapsed ? '点击折叠词库' : '点击展开词库'}
              >
                {shortName}[{library?.words.length || 0}词]：
              </span>

              {/* 已有词语列表 */}
              {library?.words.map((word) => {
                const isSelected = isActiveLibrary && temporaryWord === word.text;
                const duplicateCheck = checkCrossLibraryDuplicate(word.text);
                const isDuplicate = duplicateCheck.isDuplicate && duplicateCheck.duplicateLibraries.length > 1;
                const duplicateColor = isDuplicate ? getWordHighlightColor(word.text) : undefined;

                return (
                  <React.Fragment key={word.id}>
                    {editingWordId === word.id ? (
                      <span className="inline-flex items-center px-2 py-1 flex-shrink-0">
                        <input
                          type="text"
                          value={editingText}
                          onChange={(e) => setEditingText(e.target.value)}
                          onKeyDown={handleEditKeyDown}
                          onBlur={handleSaveEdit}
                          autoFocus
                          style={{
                            color: textColor,
                            fontSize: 14,
                            width: `${Math.max(editingText.length * 8 + 20, 60)}px`,
                            border: 'none',
                            outline: 'none',
                            background: 'transparent',
                          }}
                        />
                      </span>
                    ) : (
                      <span
                        data-word-id={word.id}
                        className="group inline-flex items-center rounded-md transition-all duration-200 flex-shrink-0 hover:bg-black hover:bg-opacity-10 cursor-pointer"
                        style={{
                          color: textColor,
                          fontSize: 14,
                          borderRadius: 6,
                          padding: 4,
                          margin: '2px 0',
                          minHeight: 28,
                          height: 28,
                          backgroundColor: isDuplicate && duplicateColor ? `${duplicateColor}30` : 'transparent',
                          border: isSelected ? '2px solid #6b7280' : '2px solid transparent'
                        }}
                        title={`${word.text} [使用${word.usagePositions?.length || 0}次] - 双击编辑，悬停显示删除按钮`}
                        onDoubleClick={() => handleStartEdit(word.id, word.text)}
                      >
                        <span className="word-text inline-flex items-center">
                          <span data-word-text={word.text} title={`词语: ${word.text}`}>{word.text}</span>
                          <span
                            className="hidden group-hover:inline transition-all duration-200 ml-0.5"
                            title={`使用次数: ${word.usagePositions?.length || 0}次`}
                          >
                            [{word.usagePositions?.length || 0}]
                          </span>
                        </span>
                        <span
                          className="hidden group-hover:inline-block ml-1 text-red-500 hover:text-red-700 text-xs font-bold transition-all duration-200 cursor-pointer"
                          onClick={(e) => {
                            e.stopPropagation();
                            handleRemoveWord(word.id);
                          }}
                          title="删除词语"
                        >
                          ×
                        </span>
                      </span>
                    )}
                    <span style={{ color: textColor, fontSize: 14 }}>，</span>
                  </React.Fragment>
                );
              })}

              {/* 输入框 */}
              <input
                ref={inputRef}
                type="text"
                value={inputValue}
                onChange={handleInputChange}
                onKeyDown={handleKeyDown}
                onBlur={handleInputConfirm}
                placeholder={placeholder}
                className="flex-shrink-0"
                style={{
                  color: textColor,
                  fontSize: 14,
                  fontFamily: 'inherit',
                  minWidth: 40,
                  maxWidth: 60,
                  border: 'none',
                  outline: 'none',
                  background: 'transparent',
                }}
              />
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default WordInput;
export type { WordInputProps };

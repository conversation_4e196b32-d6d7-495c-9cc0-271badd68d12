/**
 * Toast组件 - 单文件实现
 * 🎯 核心价值：轻量级Toast通知，黑白灰设计风格
 * 📦 功能范围：成功、错误、警告、信息四种类型的简约提示
 * 🔄 架构设计：单文件包含状态管理和UI渲染，精确追踪矩阵坐标显示
 */

'use client';

import React, { useCallback, useEffect, useState } from 'react';
import { create } from 'zustand';

// ===== 类型定义 =====

export type ToastType = 'success' | 'error' | 'warning' | 'info';
export type ToastPosition = 'matrix-center' | 'control-panel';

export interface ToastMessage {
  id: string;
  type: ToastType;
  message: string;
  duration: number;
  position?: ToastPosition; // 新增位置属性
}

// ===== 动态对齐计算 =====
// Toast位置将基于实际DOM元素尺寸动态计算，确保精确居中对齐

interface ToastStore {
  toasts: ToastMessage[];
  addToast: (message: string, type: ToastType, duration?: number, position?: ToastPosition) => void;
  removeToast: (id: string) => void;
}

// ===== 状态管理 =====

const useToastStore = create<ToastStore>((set, get) => ({
  toasts: [],

  addToast: (message, type, duration = 3000, position = 'matrix-center') => {
    const id = `toast_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`;
    const newToast: ToastMessage = { id, type, message, duration, position };

    set((state) => ({
      toasts: [...state.toasts, newToast]
    }));

    // 自动移除
    if (duration > 0) {
      setTimeout(() => {
        get().removeToast(id);
      }, duration);
    }
  },

  removeToast: (id) => {
    set((state) => ({
      toasts: state.toasts.filter(toast => toast.id !== id)
    }));
  }
}));

// ===== 样式配置 =====

const toastConfig = {
  success: {
    icon: '✓',
    bgColor: 'bg-gray-600',
    textColor: 'text-white',
    borderColor: 'border-gray-500'
  },
  error: {
    icon: '✕',
    bgColor: 'bg-black',
    textColor: 'text-white',
    borderColor: 'border-gray-800'
  },
  warning: {
    icon: '!',
    bgColor: 'bg-gray-200',
    textColor: 'text-gray-800',
    borderColor: 'border-gray-400'
  },
  info: {
    icon: 'i',
    bgColor: 'bg-white',
    textColor: 'text-gray-700',
    borderColor: 'border-gray-300'
  }
};

// ===== 单个Toast组件 =====

interface ToastItemProps {
  toast: ToastMessage;
  onClose: (id: string) => void;
  index: number;
}

const ToastItem: React.FC<ToastItemProps> = ({ toast, onClose, index }) => {
  const [isVisible, setIsVisible] = useState(false);
  const config = toastConfig[toast.type];

  useEffect(() => {
    const timer = setTimeout(() => setIsVisible(true), index * 100);
    return () => clearTimeout(timer);
  }, [index]);

  const handleClose = useCallback(() => {
    setIsVisible(false);
    setTimeout(() => onClose(toast.id), 200);
  }, [toast.id, onClose]);

  return (
    <div
      className={`
        transform transition-all duration-200 ease-out mb-2
        ${isVisible ? 'translate-y-0 opacity-100' : '-translate-y-2 opacity-0'}
      `}
    >
      <div
        className={`
          flex items-center px-4 py-3 rounded-lg border shadow-lg
          ${config.bgColor} ${config.textColor} ${config.borderColor}
          max-w-sm mx-auto backdrop-blur-sm
        `}
      >
        {/* 图标 */}
        <div className="flex-shrink-0 w-5 h-5 mr-3 flex items-center justify-center">
          <span className="text-sm font-bold">{config.icon}</span>
        </div>

        {/* 消息内容 */}
        <div className="flex-1 text-sm font-medium">
          {toast.message}
        </div>

        {/* 关闭按钮 */}
        <button
          onClick={handleClose}
          className={`
            flex-shrink-0 ml-3 w-4 h-4 flex items-center justify-center
            hover:opacity-70 transition-opacity
            ${toast.type === 'warning' ? 'text-gray-600' : 'text-gray-400'}
          `}
          aria-label="关闭"
        >
          <span className="text-xs">×</span>
        </button>
      </div>
    </div>
  );
};

// ===== Toast容器组件 =====

const Toast: React.FC = () => {
  const { toasts, removeToast } = useToastStore();

  // 分别管理两种位置的Toast
  const matrixToasts = toasts.filter(toast => toast.position === 'matrix-center');
  const controlPanelToasts = toasts.filter(toast => toast.position === 'control-panel');

  const [matrixPosition, setMatrixPosition] = useState({ left: 0, top: 0 });
  const [controlPanelPosition, setControlPanelPosition] = useState({ left: 0, top: 0 });

  // Matrix中心位置计算
  useEffect(() => {
    const updateMatrixPosition = () => {
      const matrixContainer = document.querySelector('.matrix-container') as HTMLElement;
      if (matrixContainer) {
        const containerRect = matrixContainer.getBoundingClientRect();
        const centerX = containerRect.width / 2;
        const centerY = (containerRect.height / 2) - 99; // 偏上99px
        const absoluteLeft = containerRect.left + centerX;
        const absoluteTop = containerRect.top + centerY;

        setMatrixPosition({
          left: absoluteLeft,
          top: absoluteTop
        });
      }
    };

    updateMatrixPosition();
    window.addEventListener('resize', updateMatrixPosition);
    window.addEventListener('scroll', updateMatrixPosition);

    const observer = new MutationObserver(updateMatrixPosition);
    observer.observe(document.body, {
      childList: true,
      subtree: true,
      attributes: true,
      attributeFilter: ['style', 'class']
    });

    return () => {
      window.removeEventListener('resize', updateMatrixPosition);
      window.removeEventListener('scroll', updateMatrixPosition);
      observer.disconnect();
    };
  }, []);

  // 控制面板位置计算
  useEffect(() => {
    const updateControlPanelPosition = () => {
      // 查找控制面板容器（可能的选择器）
      const controlPanel = document.querySelector('.controls-panel, .control-panel, [class*="control"], [class*="panel"]') as HTMLElement;
      if (controlPanel) {
        const panelRect = controlPanel.getBoundingClientRect();

        // 控制面板上方，水平居中
        const centerX = panelRect.width / 2;
        const offsetY = panelRect.height / 2 - 160; // 控制面板上方60px

        const absoluteLeft = panelRect.left + centerX;
        const absoluteTop = panelRect.top + offsetY;

        // console.log('🔧 控制面板Toast位置计算:', {
        //   panelRect: {
        //     left: panelRect.left,
        //     top: panelRect.top,
        //     width: panelRect.width,
        //     height: panelRect.height
        //   },
        //   centerX,
        //   offsetY,
        //   absoluteLeft,
        //   absoluteTop
        // });

        setControlPanelPosition({
          left: absoluteLeft,
          top: absoluteTop
        });
      }
    };

    updateControlPanelPosition();
    window.addEventListener('resize', updateControlPanelPosition);
    window.addEventListener('scroll', updateControlPanelPosition);

    const observer = new MutationObserver(updateControlPanelPosition);
    observer.observe(document.body, {
      childList: true,
      subtree: true,
      attributes: true,
      attributeFilter: ['style', 'class']
    });

    return () => {
      window.removeEventListener('resize', updateControlPanelPosition);
      window.removeEventListener('scroll', updateControlPanelPosition);
      observer.disconnect();
    };
  }, []);

  if (toasts.length === 0) return null;

  return (
    <>
      {/* Matrix中心位置的Toast */}
      {matrixToasts.length > 0 && (
        <div
          className="fixed z-50 pointer-events-none"
          style={{
            left: `${matrixPosition.left}px`,
            top: `${matrixPosition.top}px`,
            transform: 'translate(-50%, -50%)' // 居中对齐到计算的位置点
          }}
        >
          <div className="pointer-events-auto">
            {matrixToasts.map((toast, index) => (
              <ToastItem
                key={toast.id}
                toast={toast}
                onClose={removeToast}
                index={index}
              />
            ))}
          </div>
        </div>
      )}

      {/* 控制面板上方的Toast */}
      {controlPanelToasts.length > 0 && (
        <div
          className="fixed z-50 pointer-events-none"
          style={{
            left: `${controlPanelPosition.left}px`,
            top: `${controlPanelPosition.top}px`,
            transform: 'translate(-50%, 0)' // 水平居中，垂直不偏移
          }}
        >
          <div className="pointer-events-auto">
            {controlPanelToasts.map((toast, index) => (
              <ToastItem
                key={toast.id}
                toast={toast}
                onClose={removeToast}
                index={index}
              />
            ))}
          </div>
        </div>
      )}
    </>
  );
};

// ===== 便捷API =====

export const toast = {
  success: (message: string, duration?: number, position?: ToastPosition) => {
    useToastStore.getState().addToast(message, 'success', duration, position);
  },

  error: (message: string, duration?: number, position?: ToastPosition) => {
    useToastStore.getState().addToast(message, 'error', duration, position);
  },

  warning: (message: string, duration?: number, position?: ToastPosition) => {
    useToastStore.getState().addToast(message, 'warning', duration, position);
  },

  info: (message: string, duration?: number, position?: ToastPosition) => {
    useToastStore.getState().addToast(message, 'info', duration, position);
  },

  // 专门用于控制面板的便捷方法
  controlPanel: {
    success: (message: string, duration?: number) => {
      useToastStore.getState().addToast(message, 'success', duration, 'control-panel');
    },

    error: (message: string, duration?: number) => {
      useToastStore.getState().addToast(message, 'error', duration, 'control-panel');
    },

    warning: (message: string, duration?: number) => {
      useToastStore.getState().addToast(message, 'warning', duration, 'control-panel');
    },

    info: (message: string, duration?: number) => {
      useToastStore.getState().addToast(message, 'info', duration, 'control-panel');
    }
  }
};

export default Toast;

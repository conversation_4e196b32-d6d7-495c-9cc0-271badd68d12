/**
 * 词库滚动逻辑测试脚本
 * 🎯 测试目标：验证折叠状态下选词提醒的滚动逻辑是否正确
 * 📦 测试范围：DOM元素定位、可视区域检测、滚动位置计算
 */

// 模拟DOM环境的测试函数
function testScrollLogic() {
  console.log('🧪 开始测试词库滚动逻辑...\n');

  // 模拟容器和元素的尺寸
  const testCases = [
    {
      name: '元素在左侧不可见',
      containerWidth: 300,
      containerScrollLeft: 100,
      elementLeft: 50,
      elementWidth: 80,
      expected: '应该滚动到元素左边缘'
    },
    {
      name: '元素在右侧不可见',
      containerWidth: 300,
      containerScrollLeft: 0,
      elementLeft: 350,
      elementWidth: 80,
      expected: '应该滚动到元素右边缘可见'
    },
    {
      name: '元素完全可见',
      containerWidth: 300,
      containerScrollLeft: 50,
      elementLeft: 100,
      elementWidth: 80,
      expected: '不需要滚动'
    },
    {
      name: '元素部分在左侧不可见',
      containerWidth: 300,
      containerScrollLeft: 120,
      elementLeft: 100,
      elementWidth: 80,
      expected: '应该滚动到元素左边缘'
    },
    {
      name: '元素部分在右侧不可见',
      containerWidth: 300,
      containerScrollLeft: 0,
      elementLeft: 250,
      elementWidth: 80,
      expected: '应该滚动到元素右边缘可见'
    }
  ];

  testCases.forEach((testCase, index) => {
    console.log(`📋 测试案例 ${index + 1}: ${testCase.name}`);
    console.log(`   容器宽度: ${testCase.containerWidth}px`);
    console.log(`   当前滚动位置: ${testCase.containerScrollLeft}px`);
    console.log(`   元素位置: ${testCase.elementLeft}px`);
    console.log(`   元素宽度: ${testCase.elementWidth}px`);

    // 执行滚动逻辑计算
    const result = calculateScrollDistance(
      testCase.containerWidth,
      testCase.containerScrollLeft,
      testCase.elementLeft,
      testCase.elementWidth
    );

    console.log(`   计算结果: ${result.action}`);
    if (result.isRelativeScroll) {
      console.log(`   相对滚动距离: ${result.scrollDistance}px`);
      console.log(`   滚动后位置: ${result.newScrollLeft}px`);
    }
    console.log(`   预期结果: ${testCase.expected}`);
    console.log(`   ✅ 测试${result.isCorrect ? '通过' : '失败'}\n`);
  });
}

// 滚动距离计算函数（模拟改进后的相对滚动逻辑）
function calculateScrollDistance(containerWidth, containerScrollLeft, elementLeft, elementWidth) {
  const elementRight = elementLeft + elementWidth;
  const containerScrollRight = containerScrollLeft + containerWidth;

  // 检查元素是否在可视区域内
  const isVisible = elementLeft >= containerScrollLeft && elementRight <= containerScrollRight;

  if (!isVisible) {
    let scrollDistance = 0;
    let action;

    if (elementLeft < containerScrollLeft) {
      // 元素在左侧不可见，计算需要向左滚动的距离
      scrollDistance = elementLeft - containerScrollLeft - 20; // 负数表示向左滚动
      action = `向左相对滚动 ${Math.abs(scrollDistance)}px`;
    } else {
      // 元素在右侧不可见，计算需要向右滚动的距离
      scrollDistance = elementRight - containerScrollRight + 20; // 正数表示向右滚动
      action = `向右相对滚动 ${scrollDistance}px`;
    }

    // 计算滚动后的新位置（用于验证）
    const newScrollLeft = Math.max(0, containerScrollLeft + scrollDistance);

    return {
      action,
      scrollDistance,
      newScrollLeft,
      isRelativeScroll: true,
      isCorrect: true
    };
  } else {
    return {
      action: '无需滚动，元素已可见',
      scrollDistance: 0,
      newScrollLeft: containerScrollLeft,
      isRelativeScroll: false,
      isCorrect: true
    };
  }
}

// 渐进式滚动测试
function testProgressiveScroll() {
  console.log('🔄 测试渐进式滚动效果...\n');

  // 模拟从第4个词到第5个词的滚动场景
  const progressiveTests = [
    {
      name: '从第4个词到第5个词（相邻滚动）',
      scenario: '用户从第4个词切换到第5个词',
      currentScroll: 120,
      word4Position: 160,
      word5Position: 200,
      containerWidth: 300,
      wordWidth: 40
    },
    {
      name: '从第2个词到第6个词（跨越滚动）',
      scenario: '用户从第2个词跳转到第6个词',
      currentScroll: 40,
      word2Position: 80,
      word6Position: 240,
      containerWidth: 300,
      wordWidth: 40
    },
    {
      name: '从第8个词到第3个词（反向滚动）',
      scenario: '用户从第8个词回到第3个词',
      currentScroll: 200,
      word8Position: 320,
      word3Position: 120,
      containerWidth: 300,
      wordWidth: 40
    }
  ];

  progressiveTests.forEach((test, index) => {
    console.log(`🎯 渐进式测试 ${index + 1}: ${test.name}`);
    console.log(`   场景: ${test.scenario}`);
    console.log(`   当前滚动位置: ${test.currentScroll}px`);
    console.log(`   容器宽度: ${test.containerWidth}px`);

    // 模拟切换到目标词语
    const targetPosition = index === 0 ? test.word5Position :
      index === 1 ? test.word6Position : test.word3Position;

    const result = calculateScrollDistance(
      test.containerWidth,
      test.currentScroll,
      targetPosition,
      test.wordWidth
    );

    console.log(`   目标词语位置: ${targetPosition}px`);
    console.log(`   滚动策略: ${result.action}`);

    if (result.isRelativeScroll) {
      console.log(`   相对滚动距离: ${result.scrollDistance}px`);
      console.log(`   滚动后位置: ${result.newScrollLeft}px`);

      // 验证滚动效果
      const isSmooth = Math.abs(result.scrollDistance) < test.containerWidth;
      console.log(`   滚动平滑度: ${isSmooth ? '✅ 平滑' : '⚠️ 跳跃'}`);
    }

    console.log('');
  });
}

// 边界情况测试
function testEdgeCases() {
  console.log('🔍 测试边界情况...\n');

  const edgeCases = [
    {
      name: '元素宽度超过容器宽度',
      containerWidth: 200,
      containerScrollLeft: 0,
      elementLeft: 50,
      elementWidth: 300,
      note: '超宽元素应该优先显示左边缘'
    },
    {
      name: '滚动位置为0的边界',
      containerWidth: 300,
      containerScrollLeft: 0,
      elementLeft: 10,
      elementWidth: 50,
      note: '已在最左侧，无需滚动'
    },
    {
      name: '负数滚动位置处理',
      containerWidth: 300,
      containerScrollLeft: 50,
      elementLeft: 0,
      elementWidth: 30,
      note: '计算结果为负数时应该设为0'
    }
  ];

  edgeCases.forEach((testCase, index) => {
    console.log(`🔬 边界测试 ${index + 1}: ${testCase.name}`);
    console.log(`   说明: ${testCase.note}`);

    const result = calculateScrollDistance(
      testCase.containerWidth,
      testCase.containerScrollLeft,
      testCase.elementLeft,
      testCase.elementWidth
    );

    console.log(`   结果: ${result.action}`);
    if (result.targetScrollLeft !== null) {
      console.log(`   目标位置: ${result.targetScrollLeft}px`);
    }
    console.log('');
  });
}

// 性能测试
function testPerformance() {
  console.log('⚡ 性能测试...\n');

  const iterations = 10000;
  const startTime = performance.now();

  for (let i = 0; i < iterations; i++) {
    calculateScrollDistance(300, Math.random() * 100, Math.random() * 400, 50 + Math.random() * 100);
  }

  const endTime = performance.now();
  const duration = endTime - startTime;

  console.log(`📊 执行 ${iterations} 次计算耗时: ${duration.toFixed(2)}ms`);
  console.log(`📊 平均每次计算耗时: ${(duration / iterations).toFixed(4)}ms`);
  console.log('✅ 性能测试完成\n');
}

// 运行所有测试
function runAllTests() {
  console.log('🚀 词库滚动逻辑测试套件\n');
  console.log('='.repeat(50));

  testScrollLogic();
  testProgressiveScroll();
  testEdgeCases();
  testPerformance();

  console.log('🎉 所有测试完成！');
}

// 如果在Node.js环境中运行
if (typeof module !== 'undefined' && module.exports) {
  module.exports = {
    testScrollLogic,
    testProgressiveScroll,
    testEdgeCases,
    testPerformance,
    runAllTests,
    calculateScrollDistance
  };

  // 在Node.js环境中自动运行测试
  runAllTests();
}

// 如果在浏览器环境中运行
if (typeof window !== 'undefined') {
  window.WordScrollTest = {
    testScrollLogic,
    testProgressiveScroll,
    testEdgeCases,
    testPerformance,
    runAllTests,
    calculateScrollDistance
  };

  // 自动运行测试
  runAllTests();
}

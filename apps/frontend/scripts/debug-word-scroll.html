<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>词库滚动调试页面</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        
        .debug-container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .scroll-container {
            width: 400px;
            height: 60px;
            border: 2px solid #ddd;
            overflow-x: auto;
            overflow-y: hidden;
            background: #f9f9f9;
            margin: 20px 0;
            position: relative;
        }
        
        .word-list {
            display: flex;
            align-items: center;
            height: 100%;
            width: max-content;
            padding: 0 10px;
        }
        
        .word-item {
            display: inline-flex;
            align-items: center;
            padding: 4px 8px;
            margin: 0 2px;
            background: #e0e0e0;
            border-radius: 4px;
            cursor: pointer;
            transition: all 0.2s;
            white-space: nowrap;
            min-width: 40px;
            justify-content: center;
        }
        
        .word-item:hover {
            background: #d0d0d0;
        }
        
        .word-item.selected {
            background: #007bff;
            color: white;
            border: 2px solid #0056b3;
        }
        
        .controls {
            margin: 20px 0;
        }
        
        .btn {
            padding: 8px 16px;
            margin: 0 5px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            background: #007bff;
            color: white;
        }
        
        .btn:hover {
            background: #0056b3;
        }
        
        .info-panel {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 4px;
            padding: 15px;
            margin: 20px 0;
            font-family: monospace;
            font-size: 12px;
        }
        
        .log-panel {
            background: #1e1e1e;
            color: #d4d4d4;
            border-radius: 4px;
            padding: 15px;
            margin: 20px 0;
            font-family: monospace;
            font-size: 12px;
            max-height: 300px;
            overflow-y: auto;
        }
        
        .log-entry {
            margin: 2px 0;
            padding: 2px 0;
            border-bottom: 1px solid #333;
        }
        
        .log-debug { color: #569cd6; }
        .log-info { color: #4ec9b0; }
        .log-warn { color: #dcdcaa; }
        .log-error { color: #f44747; }
    </style>
</head>
<body>
    <div class="debug-container">
        <h1>🔍 词库滚动调试工具</h1>
        <p>这个页面用于调试词库折叠状态下的滚动逻辑问题</p>
        
        <div class="controls">
            <button class="btn" onclick="selectPrevious()">← 上一个词</button>
            <button class="btn" onclick="selectNext()">下一个词 →</button>
            <button class="btn" onclick="jumpToWord(0)">跳到第1个</button>
            <button class="btn" onclick="jumpToWord(4)">跳到第5个</button>
            <button class="btn" onclick="jumpToWord(9)">跳到第10个</button>
            <button class="btn" onclick="clearLogs()">清空日志</button>
        </div>
        
        <div class="scroll-container" id="scrollContainer">
            <div class="word-list" id="wordList">
                <!-- 词语将通过JavaScript生成 -->
            </div>
        </div>
        
        <div class="info-panel" id="infoPanel">
            <div>当前选中: <span id="currentWord">-</span></div>
            <div>选中索引: <span id="currentIndex">-</span></div>
            <div>容器滚动位置: <span id="scrollPosition">-</span></div>
            <div>容器宽度: <span id="containerWidth">-</span></div>
        </div>
        
        <div class="log-panel" id="logPanel">
            <div class="log-entry log-info">📋 调试日志将显示在这里...</div>
        </div>
    </div>

    <script>
        // 全局变量
        let currentSelectedIndex = 0;
        let words = [];
        let scrollContainer = null;
        let wordElements = [];
        
        // 初始化
        function init() {
            scrollContainer = document.getElementById('scrollContainer');
            const wordList = document.getElementById('wordList');
            
            // 生成测试词语
            for (let i = 1; i <= 20; i++) {
                words.push(`词语${i}`);
            }
            
            // 创建词语元素
            words.forEach((word, index) => {
                const wordElement = document.createElement('div');
                wordElement.className = 'word-item';
                wordElement.textContent = word;
                wordElement.onclick = () => selectWord(index);
                wordElement.id = `word-${index}`;
                wordList.appendChild(wordElement);
                wordElements.push(wordElement);
            });
            
            // 初始选中第一个词
            selectWord(0);
            
            // 监听滚动事件
            scrollContainer.addEventListener('scroll', updateInfo);
            
            log('info', '🚀 调试工具初始化完成');
        }
        
        // 选择词语
        function selectWord(index) {
            if (index < 0 || index >= words.length) return;
            
            const prevIndex = currentSelectedIndex;
            currentSelectedIndex = index;
            
            log('debug', `🎯 选择词语: ${prevIndex} → ${index} (${words[index]})`);
            
            // 更新视觉状态
            wordElements.forEach((el, i) => {
                el.classList.toggle('selected', i === index);
            });
            
            // 执行滚动逻辑
            performScrollLogic(prevIndex, index);
            
            updateInfo();
        }
        
        // 执行滚动逻辑（模拟实际代码）
        function performScrollLogic(prevIndex, currentIndex) {
            const selectedElement = wordElements[currentIndex];
            const container = scrollContainer;
            
            log('debug', `🔍 滚动调试信息: 当前索引=${currentIndex}, 前一个索引=${prevIndex}`);
            
            if (selectedElement && container) {
                // 使用getBoundingClientRect获取位置信息
                const elementRect = selectedElement.getBoundingClientRect();
                const containerRect = container.getBoundingClientRect();
                
                // 计算元素相对于容器的位置
                const elementLeft = elementRect.left - containerRect.left + container.scrollLeft;
                const elementWidth = elementRect.width;
                const elementRight = elementLeft + elementWidth;
                
                const containerScrollLeft = container.scrollLeft;
                const containerWidth = container.clientWidth;
                const containerScrollRight = containerScrollLeft + containerWidth;
                
                log('info', `📏 位置信息: 元素位置=${elementLeft.toFixed(1)}, 宽度=${elementWidth.toFixed(1)}, 容器滚动=${containerScrollLeft.toFixed(1)}, 容器宽度=${containerWidth.toFixed(1)}`);
                
                // 检查元素是否在可视区域内
                const isVisible = elementLeft >= containerScrollLeft && elementRight <= containerScrollRight;
                
                log('info', `👁️ 可见性: ${isVisible ? '可见' : '不可见'} (左边界=${elementLeft >= containerScrollLeft}, 右边界=${elementRight <= containerScrollRight})`);
                
                if (!isVisible) {
                    // 计算滚动距离
                    let scrollDistance = 0;
                    
                    if (elementLeft < containerScrollLeft) {
                        scrollDistance = elementLeft - containerScrollLeft - 20;
                        log('warn', `⬅️ 元素在左侧不可见，需要向左滚动 ${Math.abs(scrollDistance).toFixed(1)}px`);
                    } else {
                        scrollDistance = elementRight - containerScrollRight + 20;
                        log('warn', `➡️ 元素在右侧不可见，需要向右滚动 ${scrollDistance.toFixed(1)}px`);
                    }
                    
                    log('info', `🎯 滚动计算: 距离=${scrollDistance.toFixed(1)}, 当前位置=${containerScrollLeft.toFixed(1)}, 目标位置=${(containerScrollLeft + scrollDistance).toFixed(1)}`);
                    
                    // 执行滚动
                    container.scrollBy({
                        left: scrollDistance,
                        behavior: 'smooth'
                    });
                    
                    log('info', '✅ 执行滚动完成');
                } else {
                    log('info', 'ℹ️ 元素已可见，无需滚动');
                }
            }
        }
        
        // 导航函数
        function selectNext() {
            selectWord(Math.min(currentSelectedIndex + 1, words.length - 1));
        }
        
        function selectPrevious() {
            selectWord(Math.max(currentSelectedIndex - 1, 0));
        }
        
        function jumpToWord(index) {
            selectWord(index);
        }
        
        // 更新信息面板
        function updateInfo() {
            document.getElementById('currentWord').textContent = words[currentSelectedIndex] || '-';
            document.getElementById('currentIndex').textContent = currentSelectedIndex;
            document.getElementById('scrollPosition').textContent = scrollContainer.scrollLeft.toFixed(1);
            document.getElementById('containerWidth').textContent = scrollContainer.clientWidth.toFixed(1);
        }
        
        // 日志函数
        function log(level, message) {
            const logPanel = document.getElementById('logPanel');
            const timestamp = new Date().toLocaleTimeString();
            const logEntry = document.createElement('div');
            logEntry.className = `log-entry log-${level}`;
            logEntry.textContent = `[${timestamp}] ${message}`;
            logPanel.appendChild(logEntry);
            logPanel.scrollTop = logPanel.scrollHeight;
        }
        
        function clearLogs() {
            const logPanel = document.getElementById('logPanel');
            logPanel.innerHTML = '<div class="log-entry log-info">📋 日志已清空</div>';
        }
        
        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', init);
    </script>
</body>
</html>

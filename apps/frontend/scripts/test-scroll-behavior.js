/**
 * 滚动行为测试脚本
 * 在浏览器控制台中运行，用于测试实际的滚动行为
 */

// 测试滚动行为的函数
function testScrollBehavior() {
  console.log('🧪 开始测试滚动行为...');
  
  // 查找词库容器
  const wordContainers = document.querySelectorAll('[data-word-library]');
  console.log(`📋 找到 ${wordContainers.length} 个词库容器`);
  
  if (wordContainers.length === 0) {
    console.log('❌ 没有找到词库容器，请确保页面已加载完成');
    return;
  }
  
  // 测试第一个词库
  const firstContainer = wordContainers[0];
  const libraryKey = firstContainer.getAttribute('data-word-library');
  console.log(`🎯 测试词库: ${libraryKey}`);
  
  // 查找滚动容器
  const scrollContainer = firstContainer.querySelector('[class*="word-list-horizontal"]');
  if (!scrollContainer) {
    console.log('❌ 没有找到滚动容器');
    return;
  }
  
  console.log('✅ 找到滚动容器:', scrollContainer);
  
  // 查找词语元素
  const wordElements = scrollContainer.querySelectorAll('[data-word-id]');
  console.log(`📝 找到 ${wordElements.length} 个词语元素`);
  
  if (wordElements.length < 5) {
    console.log('❌ 词语数量不足，无法进行测试');
    return;
  }
  
  // 测试滚动逻辑
  testScrollLogic(scrollContainer, wordElements);
}

function testScrollLogic(container, wordElements) {
  console.log('🔍 开始测试滚动逻辑...');
  
  // 初始状态
  console.log('📊 初始状态:', {
    containerWidth: container.clientWidth,
    containerScrollLeft: container.scrollLeft,
    wordElementsCount: wordElements.length
  });
  
  // 测试场景1：模拟从第4个词到第5个词
  console.log('\n🎯 测试场景1: 从第4个词到第5个词');
  simulateWordSelection(container, wordElements, 3, 4); // 索引从0开始
  
  // 等待一段时间后测试场景2
  setTimeout(() => {
    console.log('\n🎯 测试场景2: 从第5个词到第10个词');
    simulateWordSelection(container, wordElements, 4, 9);
  }, 2000);
  
  // 等待一段时间后测试场景3
  setTimeout(() => {
    console.log('\n🎯 测试场景3: 从第10个词回到第2个词');
    simulateWordSelection(container, wordElements, 9, 1);
  }, 4000);
}

function simulateWordSelection(container, wordElements, fromIndex, toIndex) {
  if (toIndex >= wordElements.length) {
    console.log(`❌ 目标索引 ${toIndex} 超出范围`);
    return;
  }
  
  const fromElement = wordElements[fromIndex];
  const toElement = wordElements[toIndex];
  
  console.log(`📍 从词语 ${fromIndex + 1} 到词语 ${toIndex + 1}`);
  console.log('当前滚动位置:', container.scrollLeft);
  
  // 获取元素位置信息
  const fromRect = fromElement.getBoundingClientRect();
  const toRect = toElement.getBoundingClientRect();
  const containerRect = container.getBoundingClientRect();
  
  console.log('元素位置信息:', {
    from: {
      left: fromRect.left,
      right: fromRect.right,
      width: fromRect.width
    },
    to: {
      left: toRect.left,
      right: toRect.right,
      width: toRect.width
    },
    container: {
      left: containerRect.left,
      right: containerRect.right,
      width: containerRect.width
    }
  });
  
  // 计算目标元素相对于容器的位置
  const elementLeft = toRect.left - containerRect.left + container.scrollLeft;
  const elementWidth = toRect.width;
  const elementRight = elementLeft + elementWidth;
  
  const containerScrollLeft = container.scrollLeft;
  const containerWidth = container.clientWidth;
  const containerScrollRight = containerScrollLeft + containerWidth;
  
  console.log('滚动计算:', {
    elementLeft: elementLeft.toFixed(1),
    elementRight: elementRight.toFixed(1),
    containerScrollLeft: containerScrollLeft.toFixed(1),
    containerScrollRight: containerScrollRight.toFixed(1)
  });
  
  // 检查可见性
  const isVisible = elementLeft >= containerScrollLeft && elementRight <= containerScrollRight;
  console.log('可见性检查:', isVisible);
  
  if (!isVisible) {
    let scrollDistance = 0;
    
    if (elementLeft < containerScrollLeft) {
      scrollDistance = elementLeft - containerScrollLeft - 20;
      console.log(`需要向左滚动: ${Math.abs(scrollDistance).toFixed(1)}px`);
    } else {
      scrollDistance = elementRight - containerScrollRight + 20;
      console.log(`需要向右滚动: ${scrollDistance.toFixed(1)}px`);
    }
    
    console.log('执行滚动前位置:', container.scrollLeft);
    
    // 执行滚动
    container.scrollBy({
      left: scrollDistance,
      behavior: 'smooth'
    });
    
    // 检查滚动后的位置
    setTimeout(() => {
      console.log('滚动后位置:', container.scrollLeft);
      console.log('滚动距离:', container.scrollLeft - containerScrollLeft);
    }, 500);
  } else {
    console.log('元素已可见，无需滚动');
  }
}

// 监听键盘事件进行测试
function setupKeyboardTest() {
  console.log('⌨️ 设置键盘测试 (按 T 键开始测试)');
  
  document.addEventListener('keydown', (e) => {
    if (e.key.toLowerCase() === 't' && !e.ctrlKey && !e.metaKey) {
      e.preventDefault();
      testScrollBehavior();
    }
  });
}

// 自动运行
console.log('🚀 滚动行为测试脚本已加载');
console.log('📋 使用方法:');
console.log('  1. testScrollBehavior() - 运行完整测试');
console.log('  2. setupKeyboardTest() - 设置键盘快捷键测试 (按T键)');
console.log('  3. 或者直接按 T 键开始测试');

// 自动设置键盘测试
setupKeyboardTest();

// 导出函数供手动调用
window.scrollTest = {
  testScrollBehavior,
  setupKeyboardTest,
  simulateWordSelection
};
